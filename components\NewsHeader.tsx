/**
 * NewsHeader - Header component with title, stats, and action buttons
 * Displays news count, view toggle, refresh and reset buttons
 */

"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { ViewToggle } from "./view-toggle";
import { Newspaper, RefreshCw, Loader2, Trash2, Play } from "lucide-react";
import { useSync } from "@/hooks/useSync";

/**
 * Props interface for NewsHeader component
 */
interface NewsHeaderProps {
  totalNews: number;
  filteredCount: number;
  viewMode: "grid" | "list";
  onViewChange: (view: "grid" | "list") => void;
  onRefresh: () => void;
  onReset: () => Promise<void>;
  isResetting: boolean;
}

/**
 * Header component for the news management interface
 */
export function NewsHeader({
  totalNews,
  filteredCount,
  viewMode,
  onViewChange,
  onRefresh,
  onReset,
  isResetting,
}: NewsHeaderProps) {
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);

  // Sync hook
  const { isProcessing, executeSync } = useSync({
    onSuccess: (results) => {
      console.log("Sync completed:", results);
      // Optionally refresh the news data after successful sync
      onRefresh();
    },
    onError: (error) => {
      console.error("Sync failed:", error);
    },
  });

  /**
   * Generates the subtitle text based on news count and filtering
   */
  const getSubtitleText = () => {
    if (totalNews === 0) {
      return "No news found";
    }

    if (filteredCount !== totalNews) {
      return `${filteredCount} of ${totalNews} news items`;
    }

    return `${totalNews} news item${totalNews > 1 ? "s" : ""} found`;
  };

  /**
   * Handles the reset action with dialog management
   */
  const handleReset = async () => {
    try {
      await onReset();
      setIsResetDialogOpen(false);
    } catch (error) {
      // Error handling is managed by the hook
      console.error("Reset failed:", error);
    }
  };

  return (
    <div className="flex items-center justify-between mb-8">
      {/* Title and Stats */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-blue-600 rounded-lg shadow-lg">
          <Newspaper className="h-6 w-6 text-white" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">News Admin</h1>
          <p className="text-gray-600 mt-1">{getSubtitleText()}</p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center space-x-3">
        {/* View Toggle - only show if we have news */}
        {totalNews > 0 && (
          <ViewToggle view={viewMode} onViewChange={onViewChange} />
        )}

        {/* Refresh Button */}
        <Button
          onClick={onRefresh}
          variant="outline"
          size="sm"
          className="flex items-center space-x-2 shadow-xs hover:shadow-md transition-shadow"
          disabled={isResetting || isProcessing}
        >
          <RefreshCw className="h-4 w-4" />
          <span>Refresh</span>
        </Button>

        {/* Sync Button */}
        <Button
          onClick={executeSync}
          variant="outline"
          size="sm"
          className="flex items-center space-x-2 shadow-xs hover:shadow-md transition-shadow text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300"
          disabled={isResetting || isProcessing}
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Processando...</span>
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              <span>Get</span>
            </>
          )}
        </Button>

        {/* Reset Button with Confirmation Dialog */}
        <AlertDialog
          open={isResetDialogOpen}
          onOpenChange={setIsResetDialogOpen}
        >
          <AlertDialogTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={isResetting || isProcessing || totalNews === 0}
              className="flex items-center space-x-2 shadow-xs hover:shadow-md transition-shadow text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
            >
              {isResetting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Resetting...</span>
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  <span>Reset</span>
                </>
              )}
            </Button>
          </AlertDialogTrigger>

          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Complete Reset</AlertDialogTitle>
              <AlertDialogDescription>
                This action will{" "}
                <strong>permanently remove all {totalNews} news items</strong>{" "}
                from the database. This operation cannot be undone.
                <br />
                <br />
                Are you sure you want to continue?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleReset}
                disabled={isResetting || isProcessing}
                className="bg-red-600 hover:bg-red-700"
              >
                {isResetting ? "Resetting..." : "Yes, reset everything"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}

/**
 * Type exports for use in other modules
 */
export type { NewsHeaderProps };
