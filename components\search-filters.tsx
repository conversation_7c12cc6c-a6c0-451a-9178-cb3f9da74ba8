"use client";

import { useState, useMemo, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Search,
  Filter,
  CalendarIcon,
  X,
  SlidersHorizontal,
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";
import {
  FILTER_VALUES,
  FILTER_LABELS,
  MAJOR_NEWS_SOURCES,
} from "@/lib/constants";
import { useDebouncedCallback } from "@/hooks/useDebounce";

interface SearchFiltersProps {
  onSearch: (query: string) => void;
  onFilterByAuthor: (author: string) => void;
  onFilterByDate: (date: Date | undefined) => void;
  onSortChange: (sort: string) => void;
  onClearFilters: () => void;
  authors: string[];
  activeFilters: {
    search: string;
    author: string;
    date: Date | undefined;
    sort: string;
  };
}

export function SearchFilters({
  onSearch,
  onFilterByAuthor,
  onFilterByDate,
  onSortChange,
  onClearFilters,
  authors,
  activeFilters,
}: SearchFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [localSearchValue, setLocalSearchValue] = useState(
    activeFilters.search
  );

  // Debounced search to improve performance
  const debouncedSearch = useDebouncedCallback((value: string) => {
    onSearch(value);
  }, 300);

  // Get the current author filter value, handling empty string as "all"
  const currentAuthorFilter =
    activeFilters.author === ""
      ? FILTER_VALUES.ALL
      : activeFilters.author || FILTER_VALUES.PRINCIPAIS;

  // Get the display label for the current filter
  const getAuthorDisplayLabel = useMemo(() => {
    if (!currentAuthorFilter || currentAuthorFilter === FILTER_VALUES.ALL) {
      return FILTER_LABELS.all;
    }
    if (currentAuthorFilter === FILTER_VALUES.PRINCIPAIS) {
      return FILTER_LABELS.principais;
    }
    return currentAuthorFilter; // Individual author name
  }, [currentAuthorFilter]);

  const hasActiveFilters =
    activeFilters.search ||
    (activeFilters.author &&
      activeFilters.author !== FILTER_VALUES.PRINCIPAIS &&
      activeFilters.author !== "") ||
    activeFilters.date;

  const handleAuthorChange = useCallback(
    (value: string) => {
      onFilterByAuthor(value === FILTER_VALUES.ALL ? "" : value);
    },
    [onFilterByAuthor]
  );

  const handleSearchChange = useCallback(
    (value: string) => {
      setLocalSearchValue(value);
      debouncedSearch(value);
    },
    [debouncedSearch]
  );

  // Sync local search value with external changes
  useMemo(() => {
    if (activeFilters.search !== localSearchValue) {
      setLocalSearchValue(activeFilters.search);
    }
  }, [activeFilters.search, localSearchValue]);

  return (
    <div className="space-y-4 bg-white p-4 rounded-lg border shadow-xs">
      {/* Busca Principal */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar notícias por título, conteúdo ou autor..."
            value={localSearchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 h-11"
          />
        </div>
        <Button
          variant="outline"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={cn(
            "h-11 px-3 transition-all duration-200",
            showAdvanced
              ? "bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
              : "hover:bg-gray-50"
          )}
          title={
            showAdvanced
              ? "Ocultar filtros avançados"
              : "Mostrar filtros avançados"
          }
        >
          <SlidersHorizontal
            className={cn(
              "h-4 w-4 transition-transform duration-200",
              showAdvanced && "rotate-180"
            )}
          />
        </Button>
      </div>

      {/* Filtros Avançados */}
      {showAdvanced && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
          {/* Filtro por Autor */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <span>Autor</span>
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                {currentAuthorFilter === FILTER_VALUES.PRINCIPAIS
                  ? "Principais fontes"
                  : currentAuthorFilter === FILTER_VALUES.ALL
                  ? "Todas as fontes"
                  : "Fonte específica"}
              </span>
            </label>
            <Select
              value={currentAuthorFilter}
              onValueChange={handleAuthorChange}
            >
              <SelectTrigger className="h-11 bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors">
                <SelectValue>
                  <span className="flex items-center gap-2">
                    {currentAuthorFilter === FILTER_VALUES.PRINCIPAIS && (
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    )}
                    {currentAuthorFilter === FILTER_VALUES.ALL && (
                      <span className="w-2 h-2 bg-gray-500 rounded-full"></span>
                    )}
                    {currentAuthorFilter !== FILTER_VALUES.PRINCIPAIS &&
                      currentAuthorFilter !== FILTER_VALUES.ALL && (
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      )}
                    {getAuthorDisplayLabel}
                  </span>
                </SelectValue>
              </SelectTrigger>
              <SelectContent className="max-h-80">
                <SelectItem
                  value={FILTER_VALUES.PRINCIPAIS}
                  className="font-medium"
                >
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    {FILTER_LABELS.principais}
                    <span className="text-xs text-gray-500 ml-auto">
                      ({MAJOR_NEWS_SOURCES.length} fontes)
                    </span>
                  </div>
                </SelectItem>
                <SelectItem value={FILTER_VALUES.ALL} className="font-medium">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 bg-gray-500 rounded-full"></span>
                    {FILTER_LABELS.all}
                    <span className="text-xs text-gray-500 ml-auto">
                      ({authors.length} fontes)
                    </span>
                  </div>
                </SelectItem>
                {authors.length > 0 && (
                  <>
                    <div className="px-2 py-1.5 text-xs font-medium text-gray-500 border-t">
                      Fontes individuais
                    </div>
                    {authors.map((author) => (
                      <SelectItem key={author} value={author} className="pl-6">
                        <div className="flex items-center gap-2">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          {author}
                        </div>
                      </SelectItem>
                    ))}
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Data */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Data</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !activeFilters.date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {activeFilters.date
                    ? format(activeFilters.date, "PPP", { locale: ptBR })
                    : "Selecionar data"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={activeFilters.date}
                  onSelect={onFilterByDate}
                  autoFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Ordenação */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Ordenar por
            </label>
            <Select value={activeFilters.sort} onValueChange={onSortChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">Mais recentes</SelectItem>
                <SelectItem value="date-asc">Mais antigas</SelectItem>
                <SelectItem value="author">Autor (A-Z)</SelectItem>
                <SelectItem value="title">Título (A-Z)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Filtros Ativos */}
      {hasActiveFilters && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex flex-wrap gap-2 items-center">
            <span className="text-sm font-medium text-blue-700 flex items-center gap-1">
              <Filter className="h-3 w-3" />
              Filtros ativos:
            </span>
            {activeFilters.search && (
              <Badge
                variant="secondary"
                className="flex items-center gap-1 bg-white border-blue-200"
              >
                <Search className="h-3 w-3" />
                Busca: {activeFilters.search}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-red-600 transition-colors"
                  onClick={() => onSearch("")}
                />
              </Badge>
            )}
            {currentAuthorFilter !== FILTER_VALUES.PRINCIPAIS && (
              <Badge
                variant="secondary"
                className="flex items-center gap-1 bg-white border-blue-200"
              >
                <span
                  className={cn(
                    "w-2 h-2 rounded-full",
                    currentAuthorFilter === FILTER_VALUES.ALL
                      ? "bg-gray-500"
                      : "bg-green-500"
                  )}
                ></span>
                Autor:{" "}
                {currentAuthorFilter === FILTER_VALUES.ALL
                  ? FILTER_LABELS.all
                  : currentAuthorFilter}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-red-600 transition-colors"
                  onClick={() => onFilterByAuthor(FILTER_VALUES.PRINCIPAIS)}
                />
              </Badge>
            )}
            {activeFilters.date && (
              <Badge
                variant="secondary"
                className="flex items-center gap-1 bg-white border-blue-200"
              >
                <CalendarIcon className="h-3 w-3" />
                Data: {format(activeFilters.date, "dd/MM/yyyy")}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-red-600 transition-colors"
                  onClick={() => onFilterByDate(undefined)}
                />
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors ml-auto"
            >
              <X className="h-3 w-3 mr-1" />
              Limpar todos
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
